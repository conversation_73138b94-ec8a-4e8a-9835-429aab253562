{"name": "indian-railways-reports", "version": "0.1.0", "private": true, "dependencies": {"@supabase/supabase-js": "^2.39.0", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-router-dom": "^5.3.3", "@types/react-signature-canvas": "^1.0.5", "docx-templates": "^4.11.5", "pdf-lib": "^1.17.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-signature-canvas": "^1.0.6", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:prod": "npm run build && echo 'Production build completed!'", "preview": "npm run build && npx serve -s build", "deploy:vercel": "npm run build && vercel --prod", "deploy:netlify": "npm run build && netlify deploy --prod --dir=build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}