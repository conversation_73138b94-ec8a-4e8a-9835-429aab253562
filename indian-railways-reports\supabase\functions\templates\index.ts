import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    if (req.method === 'GET') {
      // Get all templates
      const { data: templates, error } = await supabaseClient
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      return new Response(
        JSON.stringify(templates),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    if (req.method === 'POST') {
      // Upload new template
      const formData = await req.formData()
      const file = formData.get('file') as File
      const name = formData.get('name') as string
      const type = formData.get('type') as string

      if (!file || !name || !type) {
        return new Response(
          JSON.stringify({ error: 'Missing required fields' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400 
          }
        )
      }

      // Upload file to Supabase Storage
      const fileName = `${Date.now()}-${file.name}`
      const { data: uploadData, error: uploadError } = await supabaseClient.storage
        .from('templates')
        .upload(fileName, file)

      if (uploadError) throw uploadError

      // Get public URL
      const { data: { publicUrl } } = supabaseClient.storage
        .from('templates')
        .getPublicUrl(fileName)

      // Extract placeholders from file (simplified - would need actual docx parsing)
      const placeholderMap = await extractPlaceholders(file)

      // Save template metadata
      const { data: template, error: dbError } = await supabaseClient
        .from('templates')
        .insert({
          name,
          type,
          file_url: publicUrl,
          placeholder_map: placeholderMap
        })
        .select()
        .single()

      if (dbError) throw dbError

      return new Response(
        JSON.stringify(template),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 201 
        }
      )
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 405 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

// Simplified placeholder extraction - in production, use proper docx parsing
async function extractPlaceholders(file: File): Promise<Record<string, string>> {
  const text = await file.text()
  const placeholders: Record<string, string> = {}
  
  // Simple regex to find {{placeholder}} patterns
  const matches = text.match(/\{\{([^}]+)\}\}/g)
  if (matches) {
    matches.forEach(match => {
      const key = match.replace(/[{}]/g, '').trim()
      placeholders[key] = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    })
  }
  
  return placeholders
}
