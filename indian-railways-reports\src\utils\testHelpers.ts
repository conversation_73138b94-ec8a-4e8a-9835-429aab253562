// Test utilities and helpers for the Indian Railways Report Generator

export interface TestTemplate {
  name: string;
  type: 'joint_report' | 'ta_form';
  placeholders: Record<string, string>;
}

export interface TestReportData {
  [key: string]: string;
}

// Sample test data for Joint Report
export const sampleJointReportData: TestReportData = {
  'Date': '2024-01-15',
  'Time': '14:30',
  'Station': 'New Delhi',
  'Division': 'Northern Railway',
  'Loco_No': 'WAP-7-30343',
  'Train_No': '12951',
  'Fault_Description': 'Traction motor bearing overheating detected during routine inspection',
  'Action_Taken': 'Locomotive detached and sent for maintenance. Replacement locomotive arranged.',
  'Officer_Name': '<PERSON><PERSON>',
  'Designation': 'Assistant Loco Pilot',
  'Employee_No': 'NR-12345',
  'Remarks': 'No passenger inconvenience. Train departed with 15 minutes delay.'
};

// Sample test data for TA Form
export const sampleTAFormData: TestReportData = {
  'Officer_Name': '<PERSON><PERSON>',
  'Designation': 'Station Master',
  'Employee_No': 'NR-67890',
  'Division': 'Northern Railway',
  'From_Station': 'New Delhi',
  'To_Station': 'Mumbai Central',
  'Journey_Date': '2024-01-20',
  'Departure_Time': '16:35',
  'Arrival_Time': '08:15',
  'Purpose': 'Official training program attendance',
  'Transport_Mode': 'Railway (AC 2-Tier)',
  'Ticket_Fare': '2450',
  'Other_Expenses': '500',
  'Total_Amount': '2950',
  'Remarks': 'Training completed successfully',
  'Date': '2024-01-22'
};

// Test template configurations
export const testTemplates: TestTemplate[] = [
  {
    name: 'Joint Report Template',
    type: 'joint_report',
    placeholders: {
      'Date': 'Date',
      'Time': 'Time',
      'Station': 'Station',
      'Division': 'Division',
      'Loco_No': 'Locomotive Number',
      'Train_No': 'Train Number',
      'Fault_Description': 'Fault Description',
      'Action_Taken': 'Action Taken',
      'Officer_Name': 'Officer Name',
      'Designation': 'Designation',
      'Employee_No': 'Employee Number',
      'Remarks': 'Remarks',
      'Signature': 'Digital Signature'
    }
  },
  {
    name: 'TA Form Template',
    type: 'ta_form',
    placeholders: {
      'Officer_Name': 'Officer Name',
      'Designation': 'Designation',
      'Employee_No': 'Employee Number',
      'Division': 'Division',
      'From_Station': 'From Station',
      'To_Station': 'To Station',
      'Journey_Date': 'Journey Date',
      'Departure_Time': 'Departure Time',
      'Arrival_Time': 'Arrival Time',
      'Purpose': 'Purpose of Journey',
      'Transport_Mode': 'Mode of Transport',
      'Ticket_Fare': 'Ticket Fare',
      'Other_Expenses': 'Other Expenses',
      'Total_Amount': 'Total Amount',
      'Remarks': 'Remarks',
      'Signature': 'Digital Signature',
      'Date': 'Application Date'
    }
  }
];

// Validation helpers
export const validateTestData = (data: TestReportData, template: TestTemplate): string[] => {
  const errors: string[] = [];
  
  Object.keys(template.placeholders).forEach(key => {
    if (key !== 'Signature' && (!data[key] || data[key].trim() === '')) {
      errors.push(`Missing required field: ${template.placeholders[key]}`);
    }
  });
  
  return errors;
};

// Generate test signature data URL
export const generateTestSignature = (): string => {
  // Create a simple test signature as data URL
  const canvas = document.createElement('canvas');
  canvas.width = 300;
  canvas.height = 100;
  const ctx = canvas.getContext('2d');
  
  if (ctx) {
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    ctx.strokeStyle = 'black';
    ctx.lineWidth = 2;
    ctx.font = '20px Arial';
    ctx.fillStyle = 'black';
    ctx.fillText('Test Signature', 50, 50);
    
    // Draw a simple signature line
    ctx.beginPath();
    ctx.moveTo(50, 60);
    ctx.lineTo(250, 60);
    ctx.stroke();
  }
  
  return canvas.toDataURL('image/png');
};

// Test API endpoints
export const testEndpoints = {
  templates: '/functions/v1/templates',
  reports: '/functions/v1/reports',
  generateReport: (id: string) => `/functions/v1/generate-report/${id}`,
  uploadSignature: (id: string) => `/functions/v1/upload-signature/${id}`
};

// Mock file for testing
export const createMockDocxFile = (name: string = 'test-template.docx'): File => {
  const content = 'Mock DOCX content for testing';
  const blob = new Blob([content], { 
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
  });
  return new File([blob], name, { 
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
  });
};

// Test utilities
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const generateRandomId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

// Console test runner
export const runBasicTests = async (): Promise<void> => {
  console.log('🧪 Running basic tests...');
  
  // Test 1: Validate sample data
  console.log('📝 Testing data validation...');
  const jointReportErrors = validateTestData(sampleJointReportData, testTemplates[0]);
  const taFormErrors = validateTestData(sampleTAFormData, testTemplates[1]);
  
  if (jointReportErrors.length === 0) {
    console.log('✅ Joint Report data validation passed');
  } else {
    console.log('❌ Joint Report data validation failed:', jointReportErrors);
  }
  
  if (taFormErrors.length === 0) {
    console.log('✅ TA Form data validation passed');
  } else {
    console.log('❌ TA Form data validation failed:', taFormErrors);
  }
  
  // Test 2: Mock file creation
  console.log('📁 Testing file creation...');
  const mockFile = createMockDocxFile();
  if (mockFile.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    console.log('✅ Mock DOCX file creation passed');
  } else {
    console.log('❌ Mock DOCX file creation failed');
  }
  
  // Test 3: Signature generation
  console.log('✍️ Testing signature generation...');
  const signature = generateTestSignature();
  if (signature.startsWith('data:image/png;base64,')) {
    console.log('✅ Test signature generation passed');
  } else {
    console.log('❌ Test signature generation failed');
  }
  
  console.log('🎉 Basic tests completed!');
};

// Export for use in development
if (process.env.NODE_ENV === 'development') {
  (window as any).testHelpers = {
    runBasicTests,
    sampleJointReportData,
    sampleTAFormData,
    testTemplates,
    generateTestSignature,
    createMockDocxFile
  };
}
