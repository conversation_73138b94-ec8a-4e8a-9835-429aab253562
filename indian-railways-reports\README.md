# Indian Railways Report Generator

A comprehensive SaaS application for generating Indian Railways reports including Joint Reports and TA Forms with digital signature support.

## Features

- **Template Management**: Upload and manage Word document templates with placeholder support
- **Dynamic Form Generation**: Automatically generate forms based on template placeholders
- **Digital Signature Capture**: Touch-friendly signature capture for mobile and desktop
- **Document Generation**: Generate both DOCX and PDF versions of reports
- **User Authentication**: Secure user management with Supabase Auth
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **React Router** for navigation
- **React Signature Canvas** for signature capture
- **Supabase Client** for backend integration

### Backend
- **Supabase** (PostgreSQL, Auth, Storage, Edge Functions)
- **Edge Functions** for document processing
- **Storage Buckets** for file management

## Project Structure

```
indian-railways-reports/
├── public/                 # Static assets
├── src/
│   ├── components/        # React components
│   │   ├── Dashboard.tsx
│   │   ├── TemplateManagement.tsx
│   │   ├── CreateReport.tsx
│   │   ├── ReportView.tsx
│   │   ├── SignatureCapture.tsx
│   │   └── Header.tsx
│   ├── lib/
│   │   └── supabase.ts    # Supabase configuration
│   ├── utils/
│   │   ├── documentProcessor.ts
│   │   └── fileValidation.ts
│   ├── App.tsx
│   └── index.tsx
├── supabase/
│   └── functions/         # Edge Functions
│       ├── templates/
│       ├── reports/
│       ├── generate-report/
│       └── upload-signature/
├── sample-templates/      # Sample template files
└── package.json
```

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Templates Table
```sql
CREATE TABLE templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  type TEXT CHECK (type IN ('joint_report', 'ta_form')) NOT NULL,
  file_url TEXT NOT NULL,
  placeholder_map JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Reports Table
```sql
CREATE TABLE reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  template_id UUID REFERENCES templates(id) ON DELETE CASCADE,
  data JSONB NOT NULL,
  generated_doc_url TEXT,
  generated_pdf_url TEXT,
  signature_url TEXT,
  status TEXT CHECK (status IN ('draft', 'generated')) DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Setup Instructions

### Prerequisites
- Node.js 18+ and npm
- Supabase account and project

### 1. Clone and Install
```bash
git clone <repository-url>
cd indian-railways-reports
npm install
```

### 2. Environment Configuration
Create a `.env` file in the root directory:
```env
REACT_APP_SUPABASE_URL=https://lexnztbclzychlvrauhq.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY
```

### 3. Database Setup
The database tables and storage buckets are already configured in the Supabase project.

### 4. Run Development Server
```bash
npm start
```

The application will be available at `http://localhost:3000`

## Usage Guide

### 1. Template Management
- Navigate to "Manage Templates"
- Upload .docx files with placeholders in {{placeholder}} format
- Supported placeholders include: {{Date}}, {{Loco_No}}, {{Train_No}}, {{Officer_Name}}, etc.

### 2. Creating Reports
- Click "Create New Report"
- Select a template
- Fill in the dynamic form fields
- Capture digital signature
- Generate the final document

### 3. Viewing Reports
- Access reports from the Dashboard
- View report details and signature
- Download generated PDF and DOCX files

## API Endpoints

### Templates
- `GET /functions/v1/templates` - List templates
- `POST /functions/v1/templates` - Upload template

### Reports
- `GET /functions/v1/reports` - List reports
- `POST /functions/v1/reports` - Create report
- `POST /functions/v1/generate-report/{id}` - Generate document

### Signatures
- `POST /functions/v1/upload-signature/{reportId}` - Upload signature

## Deployment

### Frontend (Vercel/Netlify)
1. Build the project: `npm run build`
2. Deploy the `build` folder to your hosting platform
3. Configure environment variables in the hosting platform

### Backend (Supabase)
The backend is already deployed as Supabase Edge Functions.

## Sample Templates

Sample template structures are provided in the `sample-templates/` directory:
- `joint-report-template.md` - Joint Report template structure
- `ta-form-template.md` - TA Form template structure

## Security Features

- Row Level Security (RLS) policies on all tables
- Secure file upload with validation
- User authentication required for all operations
- CORS configuration for API endpoints

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

For support and questions, please contact the development team.

## License

This project is proprietary software for Indian Railways use.
