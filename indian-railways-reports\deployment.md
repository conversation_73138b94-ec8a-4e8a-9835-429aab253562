# Deployment Guide

This guide covers deploying the Indian Railways Report Generator to production.

## Prerequisites

- Supabase project (already configured)
- Vercel or Netlify account for frontend hosting
- Domain name (optional)

## Backend Deployment (Supabase)

The backend is already deployed as Supabase Edge Functions. No additional deployment steps required.

### Verify Backend Status
1. Check Edge Functions are deployed:
   - `/functions/v1/templates`
   - `/functions/v1/reports`
   - `/functions/v1/generate-report`
   - `/functions/v1/upload-signature`

2. Verify Storage Buckets:
   - `templates`
   - `generated-reports`
   - `signatures`

3. Confirm Database Tables:
   - `users`
   - `templates`
   - `reports`

## Frontend Deployment

### Option 1: Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Build the Project**
   ```bash
   npm run build
   ```

3. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

4. **Configure Environment Variables in Vercel Dashboard**
   - `REACT_APP_SUPABASE_URL`: `https://lexnztbclzychlvrauhq.supabase.co`
   - `REACT_APP_SUPABASE_ANON_KEY`: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### Option 2: Netlify Deployment

1. **Build the Project**
   ```bash
   npm run build
   ```

2. **Deploy to Netlify**
   - Drag and drop the `build` folder to Netlify
   - Or connect your Git repository

3. **Configure Environment Variables in Netlify**
   - Go to Site Settings > Environment Variables
   - Add the same environment variables as above

### Option 3: Manual Hosting

1. **Build the Project**
   ```bash
   npm run build
   ```

2. **Upload Build Files**
   - Upload the contents of the `build` folder to your web server
   - Ensure the server serves `index.html` for all routes (SPA configuration)

## Environment Configuration

### Production Environment Variables
```env
REACT_APP_SUPABASE_URL=https://lexnztbclzychlvrauhq.supabase.co
REACT_APP_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY
```

## Post-Deployment Checklist

### 1. Functionality Testing
- [ ] User can access the application
- [ ] Template upload works
- [ ] Report creation works
- [ ] Signature capture works
- [ ] Document generation works
- [ ] File downloads work

### 2. Performance Testing
- [ ] Page load times are acceptable
- [ ] File uploads complete successfully
- [ ] Document generation completes in reasonable time

### 3. Security Testing
- [ ] Authentication is required for all operations
- [ ] Users can only access their own data
- [ ] File uploads are validated
- [ ] CORS is properly configured

### 4. Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

### 5. Mobile Testing
- [ ] Responsive design works on mobile
- [ ] Signature capture works on touch devices
- [ ] File uploads work on mobile
- [ ] Navigation is mobile-friendly

## Monitoring and Maintenance

### 1. Supabase Monitoring
- Monitor database usage in Supabase dashboard
- Check Edge Function logs for errors
- Monitor storage usage

### 2. Frontend Monitoring
- Set up error tracking (e.g., Sentry)
- Monitor performance metrics
- Track user analytics

### 3. Regular Maintenance
- Update dependencies regularly
- Monitor security advisories
- Backup database regularly
- Test functionality after updates

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify Edge Functions have proper CORS headers
   - Check Supabase project settings

2. **File Upload Failures**
   - Check storage bucket policies
   - Verify file size limits
   - Check network connectivity

3. **Authentication Issues**
   - Verify Supabase configuration
   - Check environment variables
   - Confirm user permissions

4. **Document Generation Failures**
   - Check Edge Function logs
   - Verify template format
   - Check placeholder mapping

### Support Contacts
- Technical Support: [contact information]
- Supabase Support: <EMAIL>
- Hosting Support: [hosting provider support]

## Scaling Considerations

### Database Scaling
- Monitor database performance
- Consider upgrading Supabase plan if needed
- Implement database indexing for large datasets

### Storage Scaling
- Monitor storage usage
- Implement file cleanup policies
- Consider CDN for file delivery

### Frontend Scaling
- Use CDN for static assets
- Implement caching strategies
- Consider server-side rendering for SEO

## Security Best Practices

1. **Keep Dependencies Updated**
   ```bash
   npm audit
   npm update
   ```

2. **Environment Variables**
   - Never commit sensitive keys to version control
   - Use different keys for development and production
   - Rotate keys regularly

3. **Access Control**
   - Implement proper user roles
   - Use Row Level Security policies
   - Regular security audits

4. **Data Protection**
   - Implement data backup strategies
   - Use HTTPS for all communications
   - Comply with data protection regulations
