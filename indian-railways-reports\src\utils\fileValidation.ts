// File validation utilities

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

// Validate DOCX file
export const validateDocxFile = (file: File): FileValidationResult => {
  const result: FileValidationResult = { isValid: true, warnings: [] };

  // Check file type
  if (file.type !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return {
      isValid: false,
      error: 'Please select a valid .docx file'
    };
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'File size must be less than 10MB'
    };
  }

  // Check file name
  if (file.name.length > 100) {
    result.warnings?.push('File name is very long, consider shortening it');
  }

  // Check for common issues
  if (file.size < 1000) {
    result.warnings?.push('File seems very small, please ensure it contains the template content');
  }

  return result;
};

// Validate signature image
export const validateSignatureImage = (file: File): FileValidationResult => {
  const result: FileValidationResult = { isValid: true, warnings: [] };

  // Check file type
  const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg'];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Please select a valid image file (PNG, JPEG)'
    };
  }

  // Check file size (max 2MB)
  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: 'Image size must be less than 2MB'
    };
  }

  return result;
};

// Format file size for display
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Get file extension
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

// Generate safe filename
export const generateSafeFilename = (originalName: string, prefix?: string): string => {
  const timestamp = Date.now();
  const extension = getFileExtension(originalName);
  const baseName = originalName.replace(/\.[^/.]+$/, '').replace(/[^a-zA-Z0-9]/g, '-');
  
  const safeName = prefix 
    ? `${prefix}-${timestamp}-${baseName}.${extension}`
    : `${timestamp}-${baseName}.${extension}`;
    
  return safeName;
};

// Check if file is a valid template
export const isValidTemplate = async (file: File): Promise<FileValidationResult> => {
  const validation = validateDocxFile(file);
  if (!validation.isValid) {
    return validation;
  }

  // Additional template-specific validation could go here
  // For example, checking for required placeholders
  
  return validation;
};

// Storage error handler
export const handleStorageError = (error: any): string => {
  if (error?.message) {
    // Common Supabase storage errors
    if (error.message.includes('Duplicate')) {
      return 'A file with this name already exists. Please rename your file or try again.';
    }
    
    if (error.message.includes('size')) {
      return 'File is too large. Please reduce the file size and try again.';
    }
    
    if (error.message.includes('type')) {
      return 'File type is not supported. Please use a valid file format.';
    }
    
    if (error.message.includes('permission')) {
      return 'You do not have permission to upload files. Please contact support.';
    }
    
    return error.message;
  }
  
  return 'An unexpected error occurred while uploading the file.';
};

// Download file helper
export const downloadFile = (url: string, filename: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.target = '_blank';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
