import React, { useState, useEffect } from 'react';
import { supabase, Template } from '../lib/supabase';

const TemplateManagement: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showUploadForm, setShowUploadForm] = useState(false);

  // Form state
  const [templateName, setTemplateName] = useState('');
  const [templateType, setTemplateType] = useState<'joint_report' | 'ta_form'>('joint_report');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      setSelectedFile(file);
      setError(null);
    } else {
      setError('Please select a valid .docx file');
      setSelectedFile(null);
    }
  };

  const handleUpload = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedFile || !templateName.trim()) {
      setError('Please provide a template name and select a file');
      return;
    }

    try {
      setUploading(true);
      setError(null);

      // Upload file to Supabase Storage
      const fileName = `${Date.now()}-${selectedFile.name}`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('templates')
        .upload(fileName, selectedFile);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('templates')
        .getPublicUrl(fileName);

      // Extract placeholders (simplified)
      const placeholderMap = await extractPlaceholders(selectedFile);

      // Save template metadata
      const { data: template, error: dbError } = await supabase
        .from('templates')
        .insert({
          name: templateName,
          type: templateType,
          file_url: publicUrl,
          placeholder_map: placeholderMap
        })
        .select()
        .single();

      if (dbError) throw dbError;

      // Reset form
      setTemplateName('');
      setTemplateType('joint_report');
      setSelectedFile(null);
      setShowUploadForm(false);
      
      // Refresh templates list
      fetchTemplates();
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const extractPlaceholders = async (file: File): Promise<Record<string, string>> => {
    // Simplified placeholder extraction
    // In production, you'd use a proper docx parsing library
    const placeholders: Record<string, string> = {};
    
    // Common placeholders for Indian Railways reports
    const commonPlaceholders = {
      'Date': 'Date',
      'Loco_No': 'Locomotive Number',
      'Train_No': 'Train Number',
      'Station': 'Station',
      'Time': 'Time',
      'Officer_Name': 'Officer Name',
      'Designation': 'Designation',
      'Signature': 'Digital Signature'
    };
    
    return commonPlaceholders;
  };

  const deleteTemplate = async (id: string, fileUrl: string) => {
    if (!window.confirm('Are you sure you want to delete this template?')) {
      return;
    }

    try {
      // Delete from database
      const { error: dbError } = await supabase
        .from('templates')
        .delete()
        .eq('id', id);

      if (dbError) throw dbError;

      // Delete file from storage
      const fileName = fileUrl.split('/').pop();
      if (fileName) {
        await supabase.storage
          .from('templates')
          .remove([fileName]);
      }

      // Refresh templates list
      fetchTemplates();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Delete failed');
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-railway-blue"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Template Management</h1>
        <button
          onClick={() => setShowUploadForm(!showUploadForm)}
          className="bg-railway-blue hover:bg-blue-800 text-white px-4 py-2 rounded-lg transition-colors duration-200"
        >
          {showUploadForm ? 'Cancel' : 'Upload Template'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {showUploadForm && (
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Upload New Template</h2>
          <form onSubmit={handleUpload} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Template Name
              </label>
              <input
                type="text"
                value={templateName}
                onChange={(e) => setTemplateName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue"
                placeholder="Enter template name"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Template Type
              </label>
              <select
                value={templateType}
                onChange={(e) => setTemplateType(e.target.value as 'joint_report' | 'ta_form')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue"
              >
                <option value="joint_report">Joint Report</option>
                <option value="ta_form">TA Form</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Template File (.docx)
              </label>
              <input
                type="file"
                accept=".docx"
                onChange={handleFileChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue"
                required
              />
            </div>
            
            <button
              type="submit"
              disabled={uploading}
              className="bg-railway-orange hover:bg-orange-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200"
            >
              {uploading ? 'Uploading...' : 'Upload Template'}
            </button>
          </form>
        </div>
      )}

      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Templates</h2>
        </div>
        
        {templates.length === 0 ? (
          <div className="px-6 py-8 text-center">
            <p className="text-gray-500">No templates found. Upload your first template to get started.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {templates.map((template) => (
                  <tr key={template.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {template.name}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">
                        {template.type.replace('_', ' ').toUpperCase()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(template.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <a
                        href={template.file_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-railway-blue hover:text-blue-800 mr-4"
                      >
                        Download
                      </a>
                      <button
                        onClick={() => deleteTemplate(template.id, template.file_url)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateManagement;
