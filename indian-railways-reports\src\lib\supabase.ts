import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://lexnztbclzychlvrauhq.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email?: string
  created_at: string
}

export interface Template {
  id: string
  user_id?: string
  name: string
  type: 'joint_report' | 'ta_form'
  file_url: string
  placeholder_map?: Record<string, string>
  created_at: string
}

export interface Report {
  id: string
  user_id?: string
  template_id: string
  data: Record<string, any>
  generated_doc_url?: string
  generated_pdf_url?: string
  signature_url?: string
  status: 'draft' | 'generated'
  created_at: string
}
