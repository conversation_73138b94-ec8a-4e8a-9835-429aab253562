import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    )

    if (req.method === 'POST') {
      const url = new URL(req.url)
      const reportId = url.pathname.split('/').pop()

      if (!reportId) {
        return new Response(
          JSON.stringify({ error: 'Report ID required' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400 
          }
        )
      }

      // Get report and template data
      const { data: report, error: reportError } = await supabaseClient
        .from('reports')
        .select(`
          *,
          templates (
            file_url,
            placeholder_map
          )
        `)
        .eq('id', reportId)
        .single()

      if (reportError) throw reportError

      // Download template file
      const templateResponse = await fetch(report.templates.file_url)
      const templateBuffer = await templateResponse.arrayBuffer()

      // Generate document (simplified - would use docx-templates in production)
      const generatedDoc = await generateDocument(templateBuffer, report.data, report.templates.placeholder_map)

      // Upload generated document
      const docFileName = `generated-${reportId}-${Date.now()}.docx`
      const { data: docUpload, error: docUploadError } = await supabaseClient.storage
        .from('generated-reports')
        .upload(docFileName, generatedDoc, {
          contentType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

      if (docUploadError) throw docUploadError

      // Get public URL for document
      const { data: { publicUrl: docUrl } } = supabaseClient.storage
        .from('generated-reports')
        .getPublicUrl(docFileName)

      // Generate PDF (simplified - would use proper conversion in production)
      const pdfBuffer = await convertToPDF(generatedDoc)
      
      // Upload PDF
      const pdfFileName = `generated-${reportId}-${Date.now()}.pdf`
      const { data: pdfUpload, error: pdfUploadError } = await supabaseClient.storage
        .from('generated-reports')
        .upload(pdfFileName, pdfBuffer, {
          contentType: 'application/pdf'
        })

      if (pdfUploadError) throw pdfUploadError

      // Get public URL for PDF
      const { data: { publicUrl: pdfUrl } } = supabaseClient.storage
        .from('generated-reports')
        .getPublicUrl(pdfFileName)

      // Update report with generated file URLs
      const { data: updatedReport, error: updateError } = await supabaseClient
        .from('reports')
        .update({
          generated_doc_url: docUrl,
          generated_pdf_url: pdfUrl,
          status: 'generated'
        })
        .eq('id', reportId)
        .select()
        .single()

      if (updateError) throw updateError

      return new Response(
        JSON.stringify(updatedReport),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 405 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})

// Document generation using docx-templates (simplified for demo)
async function generateDocument(templateBuffer: ArrayBuffer, data: Record<string, any>, placeholderMap: Record<string, string>): Promise<Uint8Array> {
  try {
    // In a real implementation, you would use docx-templates:
    // import { createReport } from 'docx-templates';
    // const report = await createReport({
    //   template: templateBuffer,
    //   data: data,
    //   cmdDelimiter: ['{{', '}}']
    // });
    // return new Uint8Array(report);

    // For now, return the original template as a placeholder
    // This would be replaced with actual document generation
    return new Uint8Array(templateBuffer);
  } catch (error) {
    console.error('Document generation error:', error);
    throw new Error('Failed to generate document');
  }
}

// PDF conversion (simplified for demo)
async function convertToPDF(docxBuffer: Uint8Array): Promise<Uint8Array> {
  try {
    // In a real implementation, you would use a service like:
    // - LibreOffice headless conversion
    // - Pandoc
    // - A cloud service like CloudConvert
    // - pdf-lib for basic PDF creation

    // For now, create a simple PDF placeholder
    const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Indian Railways Report Generated) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000206 00000 n
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`;

    return new TextEncoder().encode(pdfContent);
  } catch (error) {
    console.error('PDF conversion error:', error);
    throw new Error('Failed to convert to PDF');
  }
}
