import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase, Template, Report } from '../lib/supabase';
import SignatureCapture from './SignatureCapture';

const CreateReport: React.FC = () => {
  const navigate = useNavigate();
  
  const [templates, setTemplates] = useState<Template[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'select' | 'form' | 'signature' | 'complete'>('select');
  const [reportId, setReportId] = useState<string | null>(null);

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch templates');
    }
  };

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    setStep('form');
    
    // Initialize form data with empty values for all placeholders
    const initialData: Record<string, string> = {};
    if (template.placeholder_map) {
      Object.keys(template.placeholder_map).forEach(key => {
        initialData[key] = '';
      });
    }
    setFormData(initialData);
  };

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedTemplate) return;

    try {
      setLoading(true);
      setError(null);

      // Create report draft
      const { data: report, error: reportError } = await supabase
        .from('reports')
        .insert({
          template_id: selectedTemplate.id,
          data: formData,
          status: 'draft'
        })
        .select()
        .single();

      if (reportError) throw reportError;

      setReportId(report.id);
      setStep('signature');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create report');
    } finally {
      setLoading(false);
    }
  };

  const handleSignatureSave = async (signatureDataUrl: string) => {
    if (!reportId) return;

    try {
      setLoading(true);
      setError(null);

      // Convert data URL to blob
      const response = await fetch(signatureDataUrl);
      const blob = await response.blob();

      // Upload signature
      const fileName = `signature-${reportId}-${Date.now()}.png`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('signatures')
        .upload(fileName, blob, {
          contentType: 'image/png'
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('signatures')
        .getPublicUrl(fileName);

      // Update report with signature URL
      const { error: updateError } = await supabase
        .from('reports')
        .update({ signature_url: publicUrl })
        .eq('id', reportId);

      if (updateError) throw updateError;

      setStep('complete');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save signature');
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async () => {
    if (!reportId) return;

    try {
      setLoading(true);
      setError(null);

      // Call generate report function
      const response = await fetch(`https://lexnztbclzychlvrauhq.supabase.co/functions/v1/generate-report/${reportId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      const result = await response.json();
      
      // Navigate to report view
      navigate(`/reports/${reportId}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate report');
    } finally {
      setLoading(false);
    }
  };



  const renderTemplateSelection = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-gray-900">Select Template</h2>
      
      {templates.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No templates available. Please upload a template first.</p>
          <button
            onClick={() => navigate('/templates')}
            className="mt-4 bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200"
          >
            Manage Templates
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div
              key={template.id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => handleTemplateSelect(template)}
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {template.name}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                {template.type.replace('_', ' ').toUpperCase()}
              </p>
              <div className="text-sm text-gray-600">
                {template.placeholder_map && Object.keys(template.placeholder_map).length > 0 ? (
                  <p>{Object.keys(template.placeholder_map).length} fields</p>
                ) : (
                  <p>No fields detected</p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderForm = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-gray-900">Fill Report Details</h2>
        <button
          onClick={() => setStep('select')}
          className="text-railway-blue hover:text-blue-800"
        >
          ← Back to Templates
        </button>
      </div>
      
      <div className="bg-white shadow-lg rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Template: {selectedTemplate?.name}</h3>
        
        <form onSubmit={handleFormSubmit} className="space-y-4">
          {selectedTemplate?.placeholder_map && Object.entries(selectedTemplate.placeholder_map).map(([key, label]) => (
            <div key={key}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {label}
              </label>
              {key === 'Date' ? (
                <input
                  type="date"
                  value={formData[key] || ''}
                  onChange={(e) => handleInputChange(key, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue"
                  required
                />
              ) : key === 'Time' ? (
                <input
                  type="time"
                  value={formData[key] || ''}
                  onChange={(e) => handleInputChange(key, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue"
                  required
                />
              ) : (
                <input
                  type="text"
                  value={formData[key] || ''}
                  onChange={(e) => handleInputChange(key, e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-railway-blue"
                  placeholder={`Enter ${label.toLowerCase()}`}
                  required
                />
              )}
            </div>
          ))}
          
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white py-2 px-4 rounded-lg transition-colors duration-200"
          >
            {loading ? 'Creating...' : 'Continue to Signature'}
          </button>
        </form>
      </div>
    </div>
  );

  const renderSignature = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-gray-900">Add Digital Signature</h2>
        <button
          onClick={() => setStep('form')}
          className="text-railway-blue hover:text-blue-800"
        >
          ← Back to Form
        </button>
      </div>
      
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="space-y-4">
          <p className="text-gray-600">Please sign in the box below:</p>

          <SignatureCapture
            onSignatureSave={handleSignatureSave}
            disabled={loading}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );

  const renderComplete = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold text-gray-900">Report Created Successfully</h2>
      
      <div className="bg-white shadow-lg rounded-lg p-6 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Report Draft Created</h3>
          <p className="text-gray-600">Your report has been saved with signature. You can now generate the final document.</p>
        </div>
        
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => navigate('/')}
            className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200"
          >
            Back to Dashboard
          </button>
          <button
            onClick={generateReport}
            disabled={loading}
            className="bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200"
          >
            {loading ? 'Generating...' : 'Generate Report'}
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto">
      {error && (
        <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {step === 'select' && renderTemplateSelection()}
      {step === 'form' && renderForm()}
      {step === 'signature' && renderSignature()}
      {step === 'complete' && renderComplete()}
    </div>
  );
};

export default CreateReport;
