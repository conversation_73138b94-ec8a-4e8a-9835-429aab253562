// Document processing utilities for the frontend
// Note: Full document generation happens on the backend via Edge Functions

export interface PlaceholderMap {
  [key: string]: string;
}

export interface ReportData {
  [key: string]: string | number | Date;
}

// Extract placeholders from text content (simplified version)
export const extractPlaceholdersFromText = (text: string): PlaceholderMap => {
  const placeholders: PlaceholderMap = {};
  
  // Find all {{placeholder}} patterns
  const matches = text.match(/\{\{([^}]+)\}\}/g);
  
  if (matches) {
    matches.forEach(match => {
      const key = match.replace(/[{}]/g, '').trim();
      // Convert snake_case to Title Case for display
      const displayName = key
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
      
      placeholders[key] = displayName;
    });
  }
  
  return placeholders;
};

// Common placeholders for Indian Railways reports
export const getCommonPlaceholders = (): PlaceholderMap => {
  return {
    'Date': 'Date',
    'Time': 'Time',
    'Loco_No': 'Locomotive Number',
    'Train_No': 'Train Number',
    'Station': 'Station',
    'Division': 'Division',
    'Section': 'Section',
    'Officer_Name': 'Officer Name',
    'Designation': 'Designation',
    'Employee_No': 'Employee Number',
    'Fault_Description': 'Fault Description',
    'Action_Taken': 'Action Taken',
    'Remarks': 'Remarks',
    'Signature': 'Digital Signature'
  };
};

// Validate report data against template placeholders
export const validateReportData = (data: ReportData, placeholders: PlaceholderMap): string[] => {
  const errors: string[] = [];
  
  Object.keys(placeholders).forEach(key => {
    if (key !== 'Signature' && (!data[key] || data[key].toString().trim() === '')) {
      errors.push(`${placeholders[key]} is required`);
    }
  });
  
  return errors;
};

// Format data for display
export const formatDisplayValue = (key: string, value: string | number | Date): string => {
  if (value === null || value === undefined) return '';
  
  if (key.toLowerCase().includes('date')) {
    if (typeof value === 'string') {
      const date = new Date(value);
      return date.toLocaleDateString();
    }
  }
  
  if (key.toLowerCase().includes('time')) {
    if (typeof value === 'string') {
      return value;
    }
  }
  
  return value.toString();
};

// Convert form data to API format
export const prepareReportData = (formData: Record<string, string>): ReportData => {
  const prepared: ReportData = {};
  
  Object.entries(formData).forEach(([key, value]) => {
    if (key.toLowerCase().includes('date') && value) {
      // Ensure date is in proper format
      prepared[key] = new Date(value).toISOString().split('T')[0];
    } else {
      prepared[key] = value.trim();
    }
  });
  
  return prepared;
};
