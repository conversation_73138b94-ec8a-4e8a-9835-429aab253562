import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
    )

    if (req.method === 'POST') {
      const url = new URL(req.url)
      const reportId = url.pathname.split('/')[2] // Extract report ID from path

      if (!reportId) {
        return new Response(
          JSON.stringify({ error: 'Report ID required' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400 
          }
        )
      }

      const formData = await req.formData()
      const signatureFile = formData.get('signature') as File

      if (!signatureFile) {
        return new Response(
          JSON.stringify({ error: 'Signature file required' }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400 
          }
        )
      }

      // Upload signature to Supabase Storage
      const fileName = `signature-${reportId}-${Date.now()}.png`
      const { data: uploadData, error: uploadError } = await supabaseClient.storage
        .from('signatures')
        .upload(fileName, signatureFile, {
          contentType: 'image/png'
        })

      if (uploadError) throw uploadError

      // Get public URL
      const { data: { publicUrl } } = supabaseClient.storage
        .from('signatures')
        .getPublicUrl(fileName)

      // Update report with signature URL
      const { data: updatedReport, error: updateError } = await supabaseClient
        .from('reports')
        .update({
          signature_url: publicUrl
        })
        .eq('id', reportId)
        .select()
        .single()

      if (updateError) throw updateError

      return new Response(
        JSON.stringify({ signature_url: publicUrl }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200 
        }
      )
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 405 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
