---
type: "agent_requested"
description: "Example description"
---
# Global User Guidelines for Augment AI

## General Preferences
- Always use TypeScript for JavaScript projects
- Prefer functional programming patterns over imperative ones
- Use modern ES6+ features and syntax
- Write clean, self-documenting code with meaningful variable names
- Include JSDoc comments for all public functions and classes

## Code Style
- Use 2 spaces for indentation
- Use single quotes for strings unless containing single quotes
- Use semicolons at the end of statements
- Use arrow functions for anonymous functions
- Prefer const over let when possible

## Framework Preferences
- For React projects, use functional components with hooks
- Use Supabase as primary database and auth service
- Use Tailwind CSS for styling
- For document generation, use docx-templates for .docx and pdf-lib for PDF

## Error Handling
- Always handle errors gracefully with meaningful messages
- Use try-catch blocks for async operations

## Security
- Never expose sensitive info (API keys, secrets) in client-side code
- Use environment variables for configuration

## Testing
- Write unit tests for all functions and components
- Use Jest for testing

## Comments & Documentation
- Write comments for complex logic
- Maintain README.md files with setup instructions

## Performance
- Optimize for performance by avoiding unnecessary re-renders in React
- Use memoization where appropriate