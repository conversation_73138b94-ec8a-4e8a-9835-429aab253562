import React, { useRef, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';

interface SignatureCaptureProps {
  onSignatureSave: (signatureDataUrl: string) => void;
  onSignatureClear?: () => void;
  disabled?: boolean;
  className?: string;
}

const SignatureCapture: React.FC<SignatureCaptureProps> = ({
  onSignatureSave,
  onSignatureClear,
  disabled = false,
  className = ''
}) => {
  const signatureRef = useRef<SignatureCanvas>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  const handleClear = () => {
    if (signatureRef.current) {
      signatureRef.current.clear();
      setIsEmpty(true);
      if (onSignatureClear) {
        onSignatureClear();
      }
    }
  };

  const handleSave = () => {
    if (signatureRef.current && !isEmpty) {
      const dataUrl = signatureRef.current.toDataURL('image/png');
      onSignatureSave(dataUrl);
    }
  };

  const handleBegin = () => {
    setIsEmpty(false);
  };

  const handleEnd = () => {
    if (signatureRef.current) {
      setIsEmpty(signatureRef.current.isEmpty());
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="border-2 border-gray-300 rounded-lg bg-white">
        <SignatureCanvas
          ref={signatureRef}
          canvasProps={{
            width: 500,
            height: 200,
            className: 'signature-canvas w-full cursor-crosshair'
          }}
          onBegin={handleBegin}
          onEnd={handleEnd}
          backgroundColor="white"
          penColor="black"
        />
      </div>
      
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-600">
          {isEmpty ? 'Please sign in the box above' : 'Signature captured'}
        </p>
        
        <div className="flex space-x-3">
          <button
            type="button"
            onClick={handleClear}
            disabled={disabled || isEmpty}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-railway-blue disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
          
          <button
            type="button"
            onClick={handleSave}
            disabled={disabled || isEmpty}
            className="px-4 py-2 text-sm font-medium text-white bg-railway-blue border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-railway-blue disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Save Signature
          </button>
        </div>
      </div>
      
      {/* Instructions */}
      <div className="text-xs text-gray-500 space-y-1">
        <p>• Use your mouse or touch screen to sign</p>
        <p>• Click "Clear" to start over</p>
        <p>• Click "Save Signature" when finished</p>
      </div>
    </div>
  );
};

export default SignatureCapture;
