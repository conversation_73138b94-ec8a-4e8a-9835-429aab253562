import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import TemplateManagement from './components/TemplateManagement';
import CreateReport from './components/CreateReport';
import ReportView from './components/ReportView';
import Header from './components/Header';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/templates" element={<TemplateManagement />} />
            <Route path="/create-report" element={<CreateReport />} />
            <Route path="/reports/:id" element={<ReportView />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App;
