import React from 'react';
import { Link } from 'react-router-dom';

const Header: React.FC = () => {
  return (
    <header className="bg-railway-blue text-white shadow-lg">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold">Indian Railways</h1>
            <span className="text-railway-orange">Report Generator</span>
          </div>
          <nav className="flex space-x-6">
            <Link 
              to="/" 
              className="hover:text-railway-orange transition-colors duration-200"
            >
              Dashboard
            </Link>
            <Link 
              to="/templates" 
              className="hover:text-railway-orange transition-colors duration-200"
            >
              Templates
            </Link>
            <Link 
              to="/create-report" 
              className="hover:text-railway-orange transition-colors duration-200"
            >
              Create Report
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
