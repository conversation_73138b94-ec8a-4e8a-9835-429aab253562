import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { supabase, Report } from '../lib/supabase';

const ReportView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchReport(id);
    }
  }, [id]);

  const fetchReport = async (reportId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('reports')
        .select(`
          *,
          templates (
            name,
            type
          )
        `)
        .eq('id', reportId)
        .single();

      if (error) throw error;
      setReport(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch report');
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async () => {
    if (!report) return;

    try {
      setGenerating(true);
      setError(null);

      const response = await fetch(`https://lexnztbclzychlvrauhq.supabase.co/functions/v1/generate-report/${report.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxleG56dGJjbHp5Y2hsdnJhdWhxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5NzQyMzMsImV4cCI6MjA2OTU1MDIzM30.IvMm6wT5NFllO7Vu84Y1yjUhcjDpOJlxM-9a3HKYtKY`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to generate report');
      }

      const result = await response.json();
      setReport(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate report');
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-railway-blue"></div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Report not found</p>
        <button
          onClick={() => navigate('/')}
          className="mt-4 bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200"
        >
          Back to Dashboard
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Report Details</h1>
          <p className="text-gray-600 mt-1">
            Template: {(report as any).templates?.name} | 
            Type: {(report as any).templates?.type?.replace('_', ' ').toUpperCase()}
          </p>
        </div>
        <button
          onClick={() => navigate('/')}
          className="text-railway-blue hover:text-blue-800"
        >
          ← Back to Dashboard
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Report Data */}
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Report Data</h2>
          <div className="space-y-3">
            {Object.entries(report.data).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="font-medium text-gray-700">
                  {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:
                </span>
                <span className="text-gray-900">{value as string}</span>
              </div>
            ))}
          </div>
          
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex justify-between text-sm text-gray-500">
              <span>Status:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                report.status === 'draft' 
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {report.status.toUpperCase()}
              </span>
            </div>
            <div className="flex justify-between text-sm text-gray-500 mt-2">
              <span>Created:</span>
              <span>{new Date(report.created_at).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Signature */}
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Digital Signature</h2>
          {report.signature_url ? (
            <div className="text-center">
              <img
                src={report.signature_url}
                alt="Digital Signature"
                className="max-w-full h-auto border border-gray-300 rounded"
              />
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No signature available</p>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
        
        {report.status === 'draft' ? (
          <div className="text-center py-4">
            <p className="text-gray-600 mb-4">Generate the final document to download PDF and DOCX files.</p>
            <button
              onClick={generateReport}
              disabled={generating}
              className="bg-railway-blue hover:bg-blue-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg transition-colors duration-200"
            >
              {generating ? 'Generating...' : 'Generate Report'}
            </button>
          </div>
        ) : (
          <div className="flex justify-center space-x-4">
            {report.generated_pdf_url && (
              <a
                href={report.generated_pdf_url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-railway-orange hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 inline-block"
              >
                Download PDF
              </a>
            )}
            {report.generated_doc_url && (
              <a
                href={report.generated_doc_url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-railway-blue hover:bg-blue-800 text-white px-6 py-2 rounded-lg transition-colors duration-200 inline-block"
              >
                Download DOCX
              </a>
            )}
          </div>
        )}
      </div>

      {/* PDF Preview */}
      {report.status === 'generated' && report.generated_pdf_url && (
        <div className="bg-white shadow-lg rounded-lg p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Document Preview</h2>
          <div className="w-full h-96 border border-gray-300 rounded">
            <iframe
              src={report.generated_pdf_url}
              className="w-full h-full rounded"
              title="Report Preview"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportView;
